{"name": "langflow", "version": "1.3.4", "private": true, "scripts": {"dev": "vite --host 0.0.0.0", "start": "vite", "build": "vite build", "serve": "vite preview", "format": "npx prettier --write \"src/**/*.{js,jsx,ts,tsx,css,scss,less}\" --ignore-path .prettierignore", "check-format": "npx prettier --check \"src/**/*.{js,jsx,ts,tsx,css,scss,less}\" --ignore-path .prettierignore", "check-types": "tsc --noEmit --pretty", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\" --quiet", "lint:fix": "eslint \"src/**/*.{js,jsx,ts,tsx}\" --fix", "test": "npx playwright test --project=chromium", "test:ui": "npx playwright test --ui --project=chromium", "prepare": "husky install"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@antv/g2": "^5.3.3", "@chakra-ui/number-input": "^2.1.2", "@headlessui/react": "^2.0.4", "@hookform/resolvers": "^3.6.0", "@microsoft/fetch-event-source": "^2.0.1", "@million/lint": "^1.0.0-rc.26", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-form": "^0.0.3", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@tabler/icons-react": "^3.6.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "^5.49.2", "@tanstack/react-table": "^8.21.3", "@types/axios": "^0.14.0", "@types/styled-components": "^5.1.34", "@xyflow/react": "^12.3.6", "ace-builds": "^1.35.0", "ag-grid-community": "^32.0.2", "ag-grid-react": "^32.0.2", "ahooks": "^3.8.5", "ansi-to-html": "^0.7.2", "antd": "^5.25.4", "axios": "^1.7.4", "base64-js": "^1.5.1", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "dompurify": "^3.2.4", "dotenv": "^16.4.5", "elkjs": "^0.9.3", "emoji-regex": "^10.3.0", "esbuild": "^0.21.5", "eventsource-parser": "^1.1.2", "fetch-intercept": "^2.4.0", "file-saver": "^2.0.5", "framer-motion": "^11.2.10", "fuse.js": "^7.0.0", "immer": "^10.1.1", "jsencrypt": "^3.3.2", "lodash": "^4.17.21", "lucide-react": "^0.469.0", "million": "^3.1.11", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "openseadragon": "^4.1.1", "p-debounce": "^4.0.0", "pako": "^2.1.0", "playwright": "^1.44.1", "pretty-ms": "^9.1.0", "rc-tween-one": "^3.0.6", "react": "^18.3.1", "react-ace": "^11.0.1", "react-cookie": "^7.1.4", "react-dom": "^18.3.1", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.52.0", "react-hotkeys-hook": "^4.5.0", "react-icons": "^5.2.1", "react-laag": "^2.0.5", "react-markdown": "^8.0.7", "react-pdf": "^9.0.0", "react-pdf-highlighter": "^6.1.0", "react-router-dom": "^6.23.1", "react-sortablejs": "^6.1.4", "react-syntax-highlighter": "^15.5.0", "reactflow": "^11.11.3", "rehype-mathjax": "^4.0.3", "rehype-raw": "^6.1.1", "remark-gfm": "3.0.1", "remark-math": "^6.0.0", "shadcn-ui": "^0.9.4", "short-unique-id": "^5.2.0", "sortablejs": "^1.15.6", "styled-components": "^6.1.18", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "umi-request": "^1.4.0", "uuid": "^10.0.0", "vanilla-jsoneditor": "^2.3.3", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.3.2", "zod": "^3.23.8", "zustand": "^4.5.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx,css,scss,less}": ["npm run format"]}, "devDependencies": {"@playwright/test": "^1.44.1", "@swc/cli": "^0.5.2", "@swc/core": "^1.6.1", "@tailwindcss/typography": "^0.5.13", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/lodash": "4.17.5", "@types/node": "^20.14.2", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/sortablejs": "^1.15.8", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react-swc": "^3.7.0", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^9.1.7", "less": "^4.3.0", "lint-staged": "^16.1.2", "postcss": "^8.4.38", "prettier": "^3.3.2", "prettier-plugin-organize-imports": "^3.2.4", "prettier-plugin-tailwindcss": "^0.6.5", "sass-embedded": "^1.89.2", "simple-git-hooks": "^2.11.1", "tailwindcss": "^3.4.4", "tailwindcss-dotted-background": "^1.1.0", "typescript": "^5.4.5", "ua-parser-js": "^1.0.38", "vite": "^5.4.18"}}