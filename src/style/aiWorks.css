/* AIWorks 新增样式 */

@keyframes moveGradientBg {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: 0% 0;
  }
}

.file-upload-progress-bar-bg {
  background-image: linear-gradient(
    to right,
    rgba(232, 237, 255, 0.6) 0%,
    rgba(255, 255, 255, 0.6) 50%,
    rgba(232, 237, 255, 0.6) 100%
  );
  background-size: 200% 100%;
  animation: moveGradientBg 2s linear infinite;
}

.dropdown-button > button {
  border: none;
  background-color: #e6ebff;
  color: #0538ff !important;
}

.dropdown-button > button:hover {
  background-color: #cfd9ff !important;
}

.dropdown-button > .ant-dropdown-trigger {
  border-left: 1px solid #fff;
  border-color: #fff !important;
}

.dropdown-button > .ant-btn-sm {
  height: 28px;
  font-size: 12px;
}
