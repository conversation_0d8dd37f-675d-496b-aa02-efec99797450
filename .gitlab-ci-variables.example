# GitLab CI/CD 环境变量配置示例
# 复制此文件并在 GitLab 项目设置 > CI/CD > Variables 中配置

# Docker Registry 配置
CI_REGISTRY_USER=your-registry-username
CI_REGISTRY_PASSWORD=your-registry-password
CI_REGISTRY_IMAGE=registry.gitlab.com/your-group/your-project

# 部署环境配置
STAGING_SERVER=staging.example.com
PRODUCTION_SERVER=production.example.com
DEPLOY_USER=deploy
DEPLOY_KEY=your-ssh-private-key

# 应用配置
NODE_ENV=production
VITE_API_URL=https://api.example.com
VITE_APP_VERSION=$CI_COMMIT_SHA

# 通知配置（可选）
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...
TEAMS_WEBHOOK_URL=https://outlook.office.com/webhook/...

# 安全扫描（可选）
SONAR_TOKEN=your-sonarqube-token
SNYK_TOKEN=your-snyk-token

# 说明：
# 1. 敏感信息（如密码、密钥）应标记为 "Protected" 和 "Masked"
# 2. 环境特定的变量可以设置不同的作用域
# 3. 使用 $CI_COMMIT_SHA 等内置变量来动态设置值
