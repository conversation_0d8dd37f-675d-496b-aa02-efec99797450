# GitLab CI/CD 配置指南

## 概述

本项目使用优化后的 GitLab CI/CD 配置，支持代码质量检查、自动化测试、构建和部署。

## 流水线阶段

### 1. 准备阶段 (prepare)
- **install-dependencies**: 安装 npm 依赖，使用缓存优化构建速度

### 2. 质量检查阶段 (quality) - 并行执行
- **check-types**: TypeScript 类型检查
- **check-format**: 代码格式检查 (Prettier)
- **lint**: ESLint 语法检查

### 3. 测试阶段 (test)
- **unit-tests**: 单元测试（需要配置）
- **e2e-tests**: Playwright E2E 测试

### 4. 构建阶段 (build)
- **build**: 构建前端应用
- **build-docker**: 构建 Docker 镜像

### 5. 部署阶段 (deploy)
- **deploy-staging**: 部署到测试环境
- **deploy-production**: 部署到生产环境

## 主要优化特性

### 🚀 性能优化
- **智能缓存**: 缓存 node_modules 和 npm 缓存
- **并行执行**: 质量检查任务并行运行
- **增量构建**: 基于文件变更的缓存策略

### 📊 质量保证
- **代码覆盖率**: 支持测试覆盖率报告
- **测试报告**: JUnit 格式的测试结果
- **构建产物**: 自动保存构建结果

### 🔧 灵活配置
- **多环境支持**: 测试环境和生产环境分离
- **手动部署**: 生产环境需要手动触发
- **条件执行**: 基于分支和事件类型的智能触发

## 环境变量配置

在 GitLab 项目设置中配置以下变量：

```bash
# Docker Registry
CI_REGISTRY_USER=your-registry-user
CI_REGISTRY_PASSWORD=your-registry-password

# 部署相关
STAGING_SERVER=staging.example.com
PRODUCTION_SERVER=production.example.com
```

## 使用说明

### 触发条件
- **Merge Request**: 运行质量检查和测试
- **主分支推送**: 运行完整流水线
- **标签推送**: 触发生产环境部署

### 手动操作
- 构建阶段在 MR 中需要手动触发
- 部署阶段始终需要手动确认

## 故障排除

### 常见问题
1. **依赖安装失败**: 检查 package-lock.json 是否提交
2. **测试超时**: 调整 Playwright 配置中的超时时间
3. **内存不足**: 已设置 NODE_OPTIONS 增加内存限制

### 调试技巧
- 查看 CI/CD 日志中的详细错误信息
- 使用 `npm run ci:quality` 本地验证质量检查
- 检查缓存是否正确配置

## 扩展配置

### 添加新的质量检查
```yaml
security-scan:
  <<: *quality_template
  script:
    - npm audit --audit-level moderate
```

### 自定义部署脚本
修改 `deploy-staging` 和 `deploy-production` 任务中的部署脚本。
