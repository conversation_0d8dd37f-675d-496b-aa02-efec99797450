/* set the background color of many elements across the grid */
.ag-theme-shadcn {
  --ag-foreground-color: hsl(var(--foreground)) !important;
  --ag-background-color: hsl(var(--background)) !important;
  --ag-secondary-foreground-color: hsl(var(--secondary-foreground)) !important;
  --ag-data-color: hsl(var(--foreground)) !important;
  --ag-header-foreground-color: #070038 !important;
  --ag-header-background-color: #f9f9f9 !important;
  --ag-tooltip-background-color: hsl(var(--muted)) !important;
  --ag-disabled-foreground-color: hsl(var(--muted-foreground)) !important;
  --ag-border-color: #ebecf0 !important;
  --ag-selected-row-background-color: hsl(var(--accent)) !important;
  --ag-menu-background-color: hsl(var(--accent)) !important;
  --ag-panel-background-color: hsl(var(--accent)) !important;
  --ag-row-hover-color: hsl(var(--accent)) !important;
  --ag-header-height: 2.5rem !important;
}

.ag-theme-shadcn .ag-paging-panel {
  height: 3rem !important;
}

.ag-row .ag-cell {
  align-content: center !important;
}

.ag-cell {
  line-height: 1.25rem;
  padding-top: 0.675rem;
  padding-bottom: 0.675rem;
}

.ag-cell-wrapper > *:not(.ag-cell-value):not(.ag-group-value) {
  align-items: self-start;
  position: relative;
  top: 2px;
}

.ag-cell-wrapper > *:not(.ag-cell-value):not(.ag-group-value) {
  height: 0px;
}

.ag-cell-wrapper {
  align-items: normal !important;
}

.ag-body-horizontal-scroll-viewport,
.ag-body-vertical-scroll-viewport {
  cursor: auto;
}

.ag-body-horizontal-scroll-viewport::-webkit-scrollbar,
.ag-body-vertical-scroll-viewport::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.ag-body-horizontal-scroll-viewport::-webkit-scrollbar-track,
.ag-body-vertical-scroll-viewport::-webkit-scrollbar-track {
  background-color: hsl(var(--muted));
}

.ag-body-horizontal-scroll-viewport::-webkit-scrollbar-thumb,
.ag-body-vertical-scroll-viewport::-webkit-scrollbar-thumb {
  background-color: hsl(var(--border));
  border-radius: 999px;
}

.ag-body-horizontal-scroll-viewport::-webkit-scrollbar-thumb:hover,
.ag-body-vertical-scroll-viewport::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--placeholder-foreground));
}

.ag-paging-page-size {
  display: none;
}

.ag-row {
  cursor: pointer;
}

.ag-no-border .ag-root-wrapper {
  border: none !important;
}
.ag-no-border .ag-row {
  border-bottom: none !important;
}

.ag-no-border .ag-header {
  margin-bottom: 0.6rem !important;
}

.ag-no-border .ag-paging-panel {
  border-top: none !important;
}

.ag-no-border .ag-cell-focus:not(.ag-cell-inline-editing) {
  border: 1px solid transparent !important;
  box-shadow: none !important;
  outline: none !important;
}

/* 以下为 AIWorks 中 ag 自定义样式 */

.pagination-table .ag-root-wrapper {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.pagination-table .ag-cell {
  line-height: 22px;
  padding: 0 12px;
}

.pagination-table .ag-header-cell {
  padding-left: 12px;
  padding-right: 12px;
}
