const SvgSearchHybridIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
  >
    <path d="M15.75 15.75L12.525 12.525L15.75 15.75Z" fill="currentColor" />
    <path
      d="M1.5 11.625C3.36396 11.625 4.875 10.114 4.875 8.25C4.875 10.114 6.38604 11.625 8.25 11.625C6.38604 11.625 4.875 13.136 4.875 15C4.875 13.136 3.36396 11.625 1.5 11.625Z"
      fill="currentColor"
    />
    <path
      d="M15.75 15.75L12.525 12.525M2.43903 6.75C3.10509 4.16216 5.45424 2.25 8.25 2.25C11.5637 2.25 14.25 4.93629 14.25 8.25C14.25 11.0458 12.3378 13.3949 9.75 14.061M4.875 8.25C4.875 10.114 3.36396 11.625 1.5 11.625C3.36396 11.625 4.875 13.136 4.875 15C4.875 13.136 6.38604 11.625 8.25 11.625C6.38604 11.625 4.875 10.114 4.875 8.25Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default SvgSearchHybridIcon;
