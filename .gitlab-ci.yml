# GitLab CI/CD 配置文件
# 优化版本 - 支持缓存、并行执行、多环境部署

# 全局变量
variables:
  NODE_VERSION: "lts"
  NPM_CONFIG_CACHE: "$CI_PROJECT_DIR/.npm"
  CYPRESS_CACHE_FOLDER: "$CI_PROJECT_DIR/cache/Cypress"
  NODE_OPTIONS: "--max-old-space-size=4096"

# 缓存配置
cache:
  key:
    files:
      - package-lock.json
  paths:
    - .npm/
    - node_modules/
    - cache/Cypress/
  policy: pull-push

# 流水线阶段
stages:
  - prepare
  - quality
  - test
  - build
  - deploy

# 准备阶段 - 安装依赖
install-dependencies:
  stage: prepare
  image: node:${NODE_VERSION}
  script:
    - echo "Node.js version:" && node --version
    - echo "npm version:" && npm --version
    - npm ci --cache .npm --prefer-offline
  artifacts:
    paths:
      - node_modules/
    expire_in: 1 hour
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG

# 代码质量检查阶段（并行执行）
.quality_template: &quality_template
  stage: quality
  image: node:${NODE_VERSION}
  dependencies:
    - install-dependencies
  cache:
    policy: pull
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# TypeScript 类型检查
check-types:
  <<: *quality_template
  script:
    - echo "🔍 检查 TypeScript 类型..."
    - npm run check-types
  artifacts:
    reports:
      junit: reports/typescript-results.xml
    when: always
    expire_in: 1 week

# 代码格式检查
check-format:
  <<: *quality_template
  script:
    - echo "🎨 检查代码格式..."
    - npm run check-format
  allow_failure: false

# ESLint 语法检查
lint:
  <<: *quality_template
  script:
    - echo "🔧 执行 ESLint 检查..."
    - npm run lint
  artifacts:
    reports:
      junit: reports/eslint-results.xml
    when: always
    expire_in: 1 week

# 单元测试（如果有的话）
unit-tests:
  stage: test
  image: node:${NODE_VERSION}
  dependencies:
    - install-dependencies
  script:
    - echo "🧪 运行单元测试..."
    - npm run test:unit || echo "未配置单元测试"
  coverage: '/Lines\s*:\s*(\d+\.\d+)%/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
      junit: reports/unit-tests.xml
    paths:
      - coverage/
    when: always
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  allow_failure: true

# E2E 测试
e2e-tests:
  stage: test
  image: mcr.microsoft.com/playwright:v1.44.1-focal
  dependencies:
    - install-dependencies
  variables:
    CI: "true"
  before_script:
    - npm ci --cache .npm --prefer-offline
  script:
    - echo "🎭 运行 Playwright E2E 测试..."
    - npm run test
  artifacts:
    when: always
    paths:
      - playwright-report/
      - test-results/
    expire_in: 1 week
    reports:
      junit: test-results/results.xml
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  allow_failure: true

# 构建阶段
build:
  stage: build
  image: node:${NODE_VERSION}
  dependencies:
    - install-dependencies
  script:
    - echo "🏗️ 构建应用..."
    - npm run build
    - echo "构建完成，检查构建产物..."
    - ls -la build/
  artifacts:
    paths:
      - build/
    expire_in: 1 day
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
      allow_failure: true

# Docker 镜像构建
build-docker:
  stage: build
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_VERIFY: 1
    DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
  before_script:
    - docker info
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
  script:
    - echo "🐳 构建 Docker 镜像..."
    - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA .
    - docker build -t $CI_REGISTRY_IMAGE:latest .
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
    - docker push $CI_REGISTRY_IMAGE:latest
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG
  dependencies: []

# 部署到测试环境
deploy-staging:
  stage: deploy
  image: alpine:latest
  environment:
    name: staging
    url: https://staging.example.com
  before_script:
    - apk add --no-cache curl
  script:
    - echo "🚀 部署到测试环境..."
    - echo "部署镜像: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA"
    # 这里添加具体的部署脚本
    - echo "部署完成"
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  dependencies:
    - build-docker
  when: manual

# 部署到生产环境
deploy-production:
  stage: deploy
  image: alpine:latest
  environment:
    name: production
    url: https://production.example.com
  before_script:
    - apk add --no-cache curl
  script:
    - echo "🚀 部署到生产环境..."
    - echo "部署镜像: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA"
    # 这里添加具体的部署脚本
    - echo "生产环境部署完成"
  rules:
    - if: $CI_COMMIT_TAG
  dependencies:
    - build-docker
  when: manual
