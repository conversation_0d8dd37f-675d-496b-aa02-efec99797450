module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  extends: [
    "eslint:recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:@typescript-eslint/recommended",
  ],
  parser: "@typescript-eslint/parser",
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
      tsx: true,
    },
    ecmaVersion: "latest",
    sourceType: "module",
  },
  plugins: ["@typescript-eslint", "react", "react-hooks"],
  settings: {
    react: {
      version: "detect",
    },
  },
  rules: {
    "no-console": "off",
    "no-unused-vars": "off",
    "@typescript-eslint/no-unused-vars": [
      "warn",
      {
        argsIgnorePattern: "^_",
      },
    ],
    "react/react-in-jsx-scope": "off",
    "@typescript-eslint/no-explicit-any": "warn",
    "prefer-const": "warn",
    "no-empty": "warn",
    "@typescript-eslint/no-non-null-asserted-optional-chain": "warn",
    "no-prototype-builtins": "warn",
    "@typescript-eslint/no-unused-expressions": "warn",
    "no-async-promise-executor": "warn",
    "@typescript-eslint/no-empty-object-type": "warn",
    "@typescript-eslint/no-wrapper-object-types": "warn",
    "no-empty-pattern": "warn",
    "react/jsx-key": "warn",
    "no-constant-condition": "warn",
    "react/display-name": "warn",
    "react/prop-types": "off",
    "react/display-name": "off",
    "no-extra-boolean-cast": "warn",
    "react/no-unescaped-entities": "warn",
    "react/jsx-no-target-blank": "warn",
    "@typescript-eslint/ban-ts-comment": "warn",
    "no-var": "warn",
    "no-case-declarations": "warn",
    "no-misleading-character-class": "off",
    "react/no-unknown-property": "warn",
    "no-useless-catch": "off",
    "@typescript-eslint/no-unsafe-function-type": "warn",
  },
  ignorePatterns: [
    "node_modules/",
    "build/",
    "dist/",
    "*.config.js",
    "*.config.mjs",
    "*.config.ts",
    "vite.config.*",
    "tailwind.config.*",
    "postcss.config.*",
    "playwright.config.*",
  ],
};
