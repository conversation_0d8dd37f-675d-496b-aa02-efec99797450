import React, { useMemo, useState } from "react";

import { DocI<PERSON>, FolderIcon, RightArrowIcon } from "@/components/main/icon";
import type { YuqueFileTreeNode } from "@/types/knowledge";
import { LoadingOutlined } from "@ant-design/icons";
import { Breadcrumb, Checkbox } from "antd";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import { getAllFileKeys, ROOT_NODE_KEY } from "../../helper";

interface FileListProps {
  data?: YuqueFileTreeNode[];
  loadedKeys?: string[];
  bookId?: string;
  onSelectionChange?: (selectedKeys: { bookId: string; key: string }[]) => void;
  onLoadData?: (node: YuqueFileTreeNode) => Promise<void>;
}

const FileList: React.FC<FileListProps> = ({
  data = [],
  loadedKeys = [],
  bookId,
  onSelectionChange,
  onLoadData,
}) => {
  const [currentPath, setCurrentPath] = useState<string[]>([ROOT_NODE_KEY]);
  const [selectedKeys, setSelectedKeys] = useState<
    { bookId: string; key: string }[]
  >([]);
  const [loadingKeys, setLoadingKeys] = useState<string[]>([]);

  const rootNode = useMemo(
    () => data.find((item) => item.key === ROOT_NODE_KEY),
    [data],
  );

  const currentBookId = useMemo(() => {
    if (bookId) return bookId;

    // props 中的 bookId 为空时，说明当前语雀知识库的 Base URL 为根目录，第二层路径的 key 即为当前选中的 bookId
    return currentPath[1];
  }, [bookId, currentPath]);

  const currentFolder = useMemo(() => {
    if (!rootNode) return null;
    if (currentPath.length === 1 && currentPath[0] === ROOT_NODE_KEY)
      return rootNode;

    let tempNode: YuqueFileTreeNode | undefined = rootNode;
    for (let i = 1; i < currentPath.length; i++) {
      tempNode = tempNode?.children.find((node) => node.key === currentPath[i]);
    }
    return tempNode || null;
  }, [currentPath, rootNode]);

  const breadcrumbItems = useMemo(() => {
    if (!rootNode) return [];

    const items: { title: string; path: string[] }[] = [];
    const path: string[] = [];
    let currentNode: YuqueFileTreeNode | undefined = rootNode;

    for (const key of currentPath) {
      if (!currentNode) break;

      path.push(key);
      if (key === ROOT_NODE_KEY) {
        items.push({ title: currentNode.title, path: [...path] });
        continue;
      }

      currentNode = currentNode.children.find((node) => node.key === key);
      if (currentNode) {
        items.push({ title: currentNode.title, path: [...path] });
      }
    }
    return items;
  }, [currentPath, rootNode]);

  const handleBreadcrumbClick = (path: string[]) => {
    setCurrentPath(path);
  };

  const handleLoadData = async (node: YuqueFileTreeNode) => {
    try {
      setLoadingKeys((prev) => [...prev, node.key]);
      await onLoadData?.(node);
      setCurrentPath([...currentPath, node.key]);
    } catch (error) {
      console.error(error);
    } finally {
      setLoadingKeys((prev) => prev.filter((key) => key !== node.key));
    }
  };

  const handleNodeClick = (node: YuqueFileTreeNode) => {
    if (node.isLeaf) return;

    if (!node.children.length && !loadedKeys.includes(node.key)) {
      handleLoadData(node);
    } else if (node.children.length > 0) {
      setCurrentPath([...currentPath, node.key]);
    }
  };

  const getSelectionState = (
    node: YuqueFileTreeNode,
  ): "all" | "partial" | "none" => {
    const allFileKeys = getAllFileKeys(node);
    if (allFileKeys.length === 0) return "none";

    const selectedCount = allFileKeys.filter((key) =>
      selectedKeys.some((item) => item.key === key),
    ).length;

    if (selectedCount === 0) return "none";
    if (selectedCount === allFileKeys.length) return "all";
    return "partial";
  };

  const updateSelectedKeys = (
    selectedKeys: { bookId: string; key: string }[],
  ) => {
    setSelectedKeys(selectedKeys);
    onSelectionChange?.(selectedKeys);
  };

  const handleCheckboxChange = (
    node: YuqueFileTreeNode,
    e: CheckboxChangeEvent,
  ) => {
    const keysToChange = getAllFileKeys(node);

    let newSelectedKeys = [...selectedKeys];
    if (e.target.checked) {
      keysToChange.forEach((key) => {
        if (newSelectedKeys.some((item) => item.key === key)) return;
        newSelectedKeys.push({ bookId: currentBookId, key });
      });
    } else {
      newSelectedKeys = newSelectedKeys.filter(
        (item) => !keysToChange.includes(item.key),
      );
    }
    updateSelectedKeys(newSelectedKeys);
  };

  const { allSelected, partialSelected } = useMemo(() => {
    if (!currentFolder || currentFolder.children.length === 0) {
      return { allSelected: false, partialSelected: false };
    }
    const currentViewKeys = currentFolder.children.flatMap(getAllFileKeys);
    if (currentViewKeys.length === 0) {
      return { allSelected: false, partialSelected: false };
    }
    const selectedCount = currentViewKeys.filter((key) =>
      selectedKeys.some((item) => item.key === key),
    ).length;

    return {
      allSelected:
        selectedCount === currentViewKeys.length && currentViewKeys.length > 0,
      partialSelected:
        selectedCount > 0 && selectedCount < currentViewKeys.length,
    };
  }, [currentFolder, selectedKeys]);

  const handleSelectAllChange = (e: CheckboxChangeEvent) => {
    if (!currentFolder) return;
    const keysToChange = currentFolder.children.flatMap(getAllFileKeys);
    let newSelectedKeys = [...selectedKeys];

    if (e.target.checked) {
      keysToChange.forEach((key) => {
        if (newSelectedKeys.some((item) => item.key === key)) return;
        newSelectedKeys.push({ bookId: currentBookId, key });
      });
    } else {
      newSelectedKeys = newSelectedKeys.filter(
        (item) => !keysToChange.includes(item.key),
      );
    }
    updateSelectedKeys(newSelectedKeys);
  };

  const renderNodeSuffixIcon = (node: YuqueFileTreeNode) => {
    if (node.isLeaf) return null;

    if (loadingKeys.includes(node.key)) {
      return <LoadingOutlined className="text-base text-text-2" />;
    }
    if (node.children.length > 0 || !loadedKeys.includes(node.key)) {
      return <RightArrowIcon className="text-base text-text-2" />;
    }
    return null;
  };

  if (!currentFolder) {
    return <div>目录不存在</div>;
  }

  return (
    <div>
      <div className="mb-4 flex items-center px-2">
        <FolderIcon className="mr-2 text-lg text-primary-default" />
        <Breadcrumb separator="/">
          {breadcrumbItems.map((item, index) => (
            <Breadcrumb.Item key={item.path.join("/")}>
              {index < breadcrumbItems.length - 1 ? (
                <a
                  onClick={() => handleBreadcrumbClick(item.path)}
                  className="!text-text-1-primary"
                >
                  {item.title}
                </a>
              ) : (
                <span className="text-gray-400">{item.title}</span>
              )}
            </Breadcrumb.Item>
          ))}
        </Breadcrumb>
      </div>

      <div className="mb-4 px-2">
        <Checkbox
          indeterminate={partialSelected}
          checked={allSelected}
          onChange={handleSelectAllChange}
        >
          全选
        </Checkbox>
      </div>

      <div>
        {currentFolder.children.map((node) => {
          const selectionState = getSelectionState(node);
          const isChecked = selectionState === "all";
          const isIndeterminate = selectionState === "partial";

          return (
            <div
              key={node.key}
              className="flex items-center rounded p-2 hover:bg-gray-100"
            >
              <Checkbox
                className="mr-4"
                checked={isChecked}
                indeterminate={isIndeterminate}
                onChange={(e) => handleCheckboxChange(node, e)}
              />
              <div
                className={`flex flex-grow items-center text-base ${!node.isLeaf && node.children.length > 0 ? "cursor-pointer" : ""}`}
                onClick={() => handleNodeClick(node)}
              >
                {!node.isLeaf ? (
                  <FolderIcon className="mr-2 text-lg text-primary-default" />
                ) : (
                  <DocIcon className="mr-2 text-lg text-primary-default" />
                )}
                <span className="flex-grow text-sm">{node.title}</span>
                {renderNodeSuffixIcon(node)}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default FileList;
